/**index.wxss**/

/* 轮播图样式 - 顶部布局 */
.swiper-container {
  width: 100%;
  margin: 0;
  padding: 0;
}

.swiper {
  width: 100%;
  height: 400rpx;
  border-radius: 0;
  overflow: hidden;
}

.swiper-image {
  width: 100%;
  height: 100%;
  display: block;
}

.swiper-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.swiper-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.swiper-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 快捷功能组件样式 */
.quick-actions {
  display: flex;
  padding: 30rpx 20rpx;
  gap: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.quick-left {
  flex: 1.2;
}

.quick-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

/* 主要功能按钮样式 */
.quick-main-btn {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  height: 200rpx;
  box-sizing: border-box;
}

.quick-main-btn::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: rotate(45deg);
}

.quick-main-btn:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.quick-main-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
  z-index: 1;
}

.quick-main-content {
  flex: 1;
  z-index: 1;
}

.quick-main-title {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.quick-main-desc {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  line-height: 1.4;
}

.quick-main-arrow {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  z-index: 1;
  opacity: 0.8;
}

/* 快捷功能项样式 */
.quick-item {
  display: flex;
  align-items: center;
  padding: 25rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  height: 92rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.quick-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4rpx;
  height: 100%;
  background: linear-gradient(to bottom, #667eea, #764ba2);
}

.quick-item:active {
  transform: scale(0.98);
  background: #f8f9fa;
  transition: all 0.1s ease;
}

.quick-item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.quick-item-content {
  flex: 1;
}

.quick-item-title {
  display: block;
  color: #333;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.quick-item-desc {
  display: block;
  color: #666;
  font-size: 22rpx;
  opacity: 0.8;
}

.quick-item-arrow {
  color: #999;
  font-size: 24rpx;
  font-weight: bold;
}

/* 页面容器样式调整 */
.container {
  padding-top: 40rpx;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200rpx;
}

.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}
