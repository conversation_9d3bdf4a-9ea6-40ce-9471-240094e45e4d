/**index.wxss**/

/* 轮播图样式 - 顶部布局 */
.swiper-container {
  width: 100%;
  margin: 0;
  padding: 0;
}

.swiper {
  width: 100%;
  height: 400rpx;
  border-radius: 0;
  overflow: hidden;
}

.swiper-image {
  width: 100%;
  height: 100%;
  display: block;
}

.swiper-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.swiper-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.swiper-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 快捷功能组件样式 */
.quick-actions {
  display: flex;
  padding: 30rpx 20rpx;
  gap: 20rpx;
}

.quick-left {
  flex: 1.2;
}

.quick-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

/* 主要功能按钮样式 */
.quick-main-btn {
  display: flex;
  align-items: center;
  padding: 50rpx 30rpx;
  background: #667eea;
  border-radius: 24rpx;
  height: 240rpx;
  box-sizing: border-box;
}

.quick-main-btn:active {
  background: #5a6fd8;
}

.quick-main-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.quick-main-content {
  flex: 1;
}

.quick-main-title {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.quick-main-desc {
  display: block;
  color: white;
  font-size: 24rpx;
  line-height: 1.4;
}

.quick-main-arrow {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 快捷功能项样式 */
.quick-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  height: 110rpx;
  box-sizing: border-box;
  position: relative;
}

.quick-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4rpx;
  height: 100%;
  background: #667eea;
}

.quick-item:active {
  background: #f8f9fa;
}

.quick-item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.quick-item-content {
  flex: 1;
}

.quick-item-title {
  display: block;
  color: #333;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.quick-item-desc {
  display: block;
  color: #666;
  font-size: 22rpx;
}

.quick-item-arrow {
  color: #999;
  font-size: 24rpx;
  font-weight: bold;
}

/* 手风琴分类组件样式 */
.accordion-container {
  padding: 30rpx 20rpx;
  background: #f8f9fa;
}

.accordion-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

.accordion-scroll {
  width: 100%;
  white-space: nowrap;
}

.accordion-list {
  display: flex;
  gap: 15rpx;
}

.accordion-item {
  display: inline-block;
  min-width: 200rpx;
  background: white;
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  overflow: hidden;
  transition: all 0.3s ease;
}

.accordion-item.active {
  border-color: #667eea;
  min-width: 300rpx;
}

.accordion-header {
  padding: 25rpx 20rpx;
  text-align: center;
  cursor: pointer;
}

.accordion-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.accordion-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}

.accordion-count {
  font-size: 22rpx;
  color: #666;
}

.accordion-content {
  padding: 0 15rpx 20rpx;
  border-top: 1rpx solid #f0f0f0;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500rpx;
  }
}

.photo-item {
  display: flex;
  align-items: center;
  padding: 12rpx 10rpx;
  margin-bottom: 8rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.photo-item:last-child {
  margin-bottom: 0;
}

.photo-item:active {
  background: #e9ecef;
}

.photo-image {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
  border: 1rpx solid #e9ecef;
}

.photo-name {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

/* 页面容器样式调整 */
.container {
  padding-top: 40rpx;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200rpx;
}

.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}
