/**index.wxss**/

/* 轮播图样式 - 顶部布局 */
.swiper-container {
  width: 100%;
  margin: 0;
  padding: 0;
}

.swiper {
  width: 100%;
  height: 400rpx;
  border-radius: 0;
  overflow: hidden;
}

.swiper-image {
  width: 100%;
  height: 100%;
  display: block;
}

.swiper-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.swiper-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.swiper-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 快捷功能组件样式 */
.quick-actions {
  display: flex;
  padding: 30rpx 20rpx;
  gap: 20rpx;
}

.quick-left {
  flex: 1.2;
}

.quick-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

/* 主要功能按钮样式 */
.quick-main-btn {
  display: flex;
  align-items: center;
  padding: 50rpx 30rpx;
  background: #667eea;
  border-radius: 24rpx;
  height: 240rpx;
  box-sizing: border-box;
}

.quick-main-btn:active {
  background: #5a6fd8;
}

.quick-main-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.quick-main-content {
  flex: 1;
}

.quick-main-title {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.quick-main-desc {
  display: block;
  color: white;
  font-size: 24rpx;
  line-height: 1.4;
}

.quick-main-arrow {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 快捷功能项样式 */
.quick-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  height: 110rpx;
  box-sizing: border-box;
  position: relative;
}

.quick-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4rpx;
  height: 100%;
  background: #667eea;
}

.quick-item:active {
  background: #f8f9fa;
}

.quick-item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.quick-item-content {
  flex: 1;
}

.quick-item-title {
  display: block;
  color: #333;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.quick-item-desc {
  display: block;
  color: #666;
  font-size: 22rpx;
}

.quick-item-arrow {
  color: #999;
  font-size: 24rpx;
  font-weight: bold;
}

/* 分类筛选组件样式 */
.filter-container {
  background: white;
  padding-bottom: 20rpx;
}

/* 分类标签栏样式 */
.filter-tabs {
  display: flex;
  align-items: center;
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}

.tabs-scroll {
  flex: 1;
  white-space: nowrap;
}

.tabs-list {
  display: flex;
  padding: 0 20rpx;
  gap: 40rpx;
}

.tab-item {
  position: relative;
  padding: 10rpx 0;
  white-space: nowrap;
}

.tab-name {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.tab-item.active .tab-name {
  color: #333;
  font-weight: 600;
}

.tab-underline {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #333;
  border-radius: 2rpx;
}

.menu-icon {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #666;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
}

/* 尺寸网格样式 */
.size-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
}

.size-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 25rpx;
  background: white;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  overflow: hidden;
}

.size-item:active {
  background: #f8f9fa;
}

.size-content {
  flex: 1;
}

.size-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.size-dimensions {
  font-size: 24rpx;
  color: #999;
}

.size-arrow {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 15rpx;
}

.hot-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  font-size: 24rpx;
}

.size-item.hot {
  border-color: #333;
  background: white;
}

/* 页面容器样式调整 */
.container {
  padding-top: 40rpx;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200rpx;
}

.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}
