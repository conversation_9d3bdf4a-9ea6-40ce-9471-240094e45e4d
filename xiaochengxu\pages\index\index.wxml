<!--index.wxml-->
<!-- 轮播图组件 - 置于页面顶部 -->
<view class="swiper-container">
    <swiper class="swiper"
            indicator-dots="{{indicatorDots}}"
            autoplay="{{autoplay}}"
            interval="{{interval}}"
            duration="{{duration}}"
            circular="{{circular}}"
            indicator-color="{{indicatorColor}}"
            indicator-active-color="{{indicatorActiveColor}}">
      <block wx:for="{{swiperList}}" wx:key="id">
        <swiper-item>
          <image src="{{item.image}}"
                 class="swiper-image"
                 mode="aspectFill"
                 bindtap="onSwiperTap"
                 data-index="{{index}}"
                 data-item="{{item}}"></image>
          <view class="swiper-text" wx:if="{{item.title}}">
            <text class="swiper-title">{{item.title}}</text>
            <text class="swiper-desc" wx:if="{{item.desc}}">{{item.desc}}</text>
          </view>
        </swiper-item>
      </block>
    </swiper>
</view>

<!-- 快捷功能组件 -->
<view class="quick-actions">
  <view class="quick-left">
    <view class="quick-main-btn" bindtap="onMainActionTap">
      <view class="quick-main-icon">
        <text class="iconfont">🎯</text>
      </view>
      <view class="quick-main-content">
        <text class="quick-main-title">主要功能</text>
        <text class="quick-main-desc">快速访问核心服务</text>
      </view>
      <view class="quick-main-arrow">→</view>
    </view>
  </view>

  <view class="quick-right">
    <view class="quick-item" bindtap="onQuickActionTap" data-type="service1">
      <view class="quick-item-icon">
        <text class="iconfont">📊</text>
      </view>
      <view class="quick-item-content">
        <text class="quick-item-title">数据统计</text>
        <text class="quick-item-desc">查看详细报表</text>
      </view>
      <view class="quick-item-arrow">→</view>
    </view>

    <view class="quick-item" bindtap="onQuickActionTap" data-type="service2">
      <view class="quick-item-icon">
        <text class="iconfont">⚙️</text>
      </view>
      <view class="quick-item-content">
        <text class="quick-item-title">系统设置</text>
        <text class="quick-item-desc">个性化配置</text>
      </view>
      <view class="quick-item-arrow">→</view>
    </view>
  </view>
</view>

<!-- 手风琴分类组件 -->
<view class="accordion-container">
  <view class="accordion-title">证件照分类</view>
  <view class="accordion-list">
    <block wx:for="{{categoryList}}" wx:key="id">
      <view class="accordion-item">
        <view class="accordion-header {{item.active ? 'active' : ''}}"
              bindtap="onCategoryTap"
              data-index="{{index}}"
              data-id="{{item.id}}">
          <view class="accordion-left">
            <view class="accordion-icon">{{item.icon}}</view>
            <view class="accordion-info">
              <text class="accordion-name">{{item.name}}</text>
              <text class="accordion-count">{{item.count}}张照片</text>
            </view>
          </view>
          <view class="accordion-arrow {{item.active ? 'rotate' : ''}}">
            <text>▼</text>
          </view>
        </view>
        <view class="accordion-content {{item.active ? 'show' : ''}}">
          <view class="photo-grid">
            <block wx:for="{{item.photos}}" wx:key="id" wx:for-item="photo">
              <view class="photo-item" bindtap="onPhotoTap" data-photo="{{photo}}">
                <image class="photo-image" src="{{photo.thumbnail}}" mode="aspectFill"></image>
                <text class="photo-name">{{photo.name}}</text>
              </view>
            </block>
          </view>
        </view>
      </view>
    </block>
  </view>
</view>

<!-- 页面内容区域 -->
<view class="container">
  <view class="userinfo">
    <block wx:if="{{hasUserInfo}}">
      <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
      <text class="userinfo-nickname">{{userInfo.nickName}}</text>
    </block>
    <block wx:else>
      <button wx:if="{{!hasUserInfo && canIUse}}" open-type="getUserInfo" bindgetuserinfo="getUserInfo"> 获取头像昵称 </button>
      <view wx:else class="userinfo-avatar-wrapper">
        <image class="userinfo-avatar" src="/images/user-unlogin.png" mode="cover"></image>
      </view>
    </block>
  </view>
  <view class="usermotto">
    <text class="user-motto">{{motto}}</text>
  </view>
</view>
