<!--index.wxml-->
<view class="container">
  <view class="userinfo">
    <block wx:if="{{hasUserInfo}}">
      <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
      <text class="userinfo-nickname">{{userInfo.nickName}}</text>
    </block>
    <block wx:else>
      <button wx:if="{{!hasUserInfo && canIUse}}" open-type="getUserInfo" bindgetuserinfo="getUserInfo"> 获取头像昵称 </button>
      <view wx:else class="userinfo-avatar-wrapper">
        <image class="userinfo-avatar" src="/images/user-unlogin.png" mode="cover"></image>
      </view>
    </block>
  </view>
  <view class="usermotto">
    <text class="user-motto">{{motto}}</text>
  </view>
</view>
