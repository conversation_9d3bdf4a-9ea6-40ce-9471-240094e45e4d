<!--index.wxml-->
<!-- 轮播图组件 - 置于页面顶部 -->
<view class="swiper-container">
    <swiper class="swiper"
            indicator-dots="{{indicatorDots}}"
            autoplay="{{autoplay}}"
            interval="{{interval}}"
            duration="{{duration}}"
            circular="{{circular}}"
            indicator-color="{{indicatorColor}}"
            indicator-active-color="{{indicatorActiveColor}}">
      <block wx:for="{{swiperList}}" wx:key="id">
        <swiper-item>
          <image src="{{item.image}}"
                 class="swiper-image"
                 mode="aspectFill"
                 bindtap="onSwiperTap"
                 data-index="{{index}}"
                 data-item="{{item}}"></image>
          <view class="swiper-text" wx:if="{{item.title}}">
            <text class="swiper-title">{{item.title}}</text>
            <text class="swiper-desc" wx:if="{{item.desc}}">{{item.desc}}</text>
          </view>
        </swiper-item>
      </block>
    </swiper>
</view>

<!-- 页面内容区域 -->
<view class="container">
  <view class="userinfo">
    <block wx:if="{{hasUserInfo}}">
      <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
      <text class="userinfo-nickname">{{userInfo.nickName}}</text>
    </block>
    <block wx:else>
      <button wx:if="{{!hasUserInfo && canIUse}}" open-type="getUserInfo" bindgetuserinfo="getUserInfo"> 获取头像昵称 </button>
      <view wx:else class="userinfo-avatar-wrapper">
        <image class="userinfo-avatar" src="/images/user-unlogin.png" mode="cover"></image>
      </view>
    </block>
  </view>
  <view class="usermotto">
    <text class="user-motto">{{motto}}</text>
  </view>
</view>
