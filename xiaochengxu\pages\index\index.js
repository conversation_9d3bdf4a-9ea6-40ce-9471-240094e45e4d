// index.js
// 获取应用实例
const app = getApp()

Page({
  data: {

    // 轮播图配置
    indicatorDots: true,        // 是否显示面板指示点
    autoplay: true,             // 是否自动切换
    interval: 3000,             // 自动切换时间间隔
    duration: 500,              // 滑动动画时长
    circular: true,             // 是否采用衔接滑动
    indicatorColor: 'rgba(255, 255, 255, 0.5)',      // 指示点颜色
    indicatorActiveColor: '#ffffff',                   // 当前选中的指示点颜色

    // 轮播图数据
    swiperList: [
      {
        id: 1,
        image: 'https://img1.baidu.com/it/u=1966616150,2146512490&fm=26&fmt=auto&gp=0.jpg',
        title: '轮播图1',
        desc: '这是第一张轮播图的描述',
        url: '/pages/detail/detail?id=1'
      },
      {
        id: 2,
        image: 'https://img2.baidu.com/it/u=2967689259,2171491969&fm=26&fmt=auto&gp=0.jpg',
        title: '轮播图2',
        desc: '这是第二张轮播图的描述',
        url: '/pages/detail/detail?id=2'
      },
      {
        id: 3,
        image: 'https://img0.baidu.com/it/u=1956849617,4054837746&fm=26&fmt=auto&gp=0.jpg',
        title: '轮播图3',
        desc: '这是第三张轮播图的描述',
        url: '/pages/detail/detail?id=3'
      }
    ],

    // 所有分类标签
    allFilterTabs: [
      { id: 1, name: '热门尺寸', active: true },
      { id: 2, name: '通用尺照', active: false },
      { id: 3, name: '医药卫生', active: false },
      { id: 4, name: '语言考试', active: false },
      { id: 5, name: '教资报名', active: false },
      { id: 6, name: '计算机考试', active: false },
      { id: 7, name: '公务员考试', active: false },
      { id: 8, name: '研究生考试', active: false },
      { id: 9, name: '职业资格', active: false },
      { id: 10, name: '驾驶证照', active: false },
      { id: 11, name: '护照签证', active: false },
      { id: 12, name: '学生证件', active: false },
      { id: 13, name: '工作证件', active: false },
      { id: 14, name: '社保医保', active: false },
      { id: 15, name: '银行金融', active: false }
    ],

    // 当前显示的标签（默认显示前6个）
    visibleTabs: [],

    // 是否显示所有分类
    showAllCategories: false,

    // 当前显示的尺寸列表
    currentSizeList: [],

    // 所有尺寸数据
    allSizeData: {
      1: [ // 热门尺寸
        { id: 1, name: '一寸', size: '295×413PX', hot: true },
        { id: 2, name: '二寸', size: '413×579PX', hot: true },
        { id: 3, name: '教资报名（请选白...', size: '413×579PX', hot: true },
        { id: 4, name: '大学生图像采集（...', size: '480×640PX', hot: true },
        { id: 5, name: '计算机考试（390×...', size: '390×567PX', hot: true },
        { id: 6, name: '国家司法考试', size: '413×626PX', hot: true },
        { id: 7, name: '小一寸', size: '260×378PX', hot: false },
        { id: 8, name: '省考（代审核请联...', size: '413×579PX', hot: true }
      ],
      2: [ // 通用尺照
        { id: 9, name: '一寸', size: '295×413PX', hot: false },
        { id: 10, name: '二寸', size: '413×579PX', hot: false },
        { id: 11, name: '小二寸', size: '413×531PX', hot: false },
        { id: 12, name: '大二寸', size: '413×626PX', hot: false }
      ],
      3: [ // 医药卫生
        { id: 13, name: '执业医师', size: '413×579PX', hot: false },
        { id: 14, name: '护士资格', size: '295×413PX', hot: false },
        { id: 15, name: '药师考试', size: '413×579PX', hot: false }
      ],
      4: [ // 语言考试
        { id: 16, name: '英语四六级', size: '144×192PX', hot: false },
        { id: 17, name: '托福考试', size: '413×531PX', hot: false },
        { id: 18, name: '雅思考试', size: '413×531PX', hot: false }
      ],
      5: [ // 教资报名
        { id: 19, name: '教师资格证', size: '413×579PX', hot: true },
        { id: 20, name: '普通话考试', size: '390×567PX', hot: false }
      ],
      6: [ // 计算机考试
        { id: 21, name: '计算机二级', size: '390×567PX', hot: true },
        { id: 22, name: '软考', size: '413×579PX', hot: false }
      ],
      7: [ // 公务员考试
        { id: 23, name: '国家公务员', size: '413×579PX', hot: false },
        { id: 24, name: '省考公务员', size: '413×579PX', hot: false }
      ],
      8: [ // 研究生考试
        { id: 25, name: '研究生报名', size: '480×640PX', hot: false },
        { id: 26, name: '博士报名', size: '480×640PX', hot: false }
      ],
      9: [ // 职业资格
        { id: 27, name: '会计师考试', size: '413×579PX', hot: false },
        { id: 28, name: '建造师考试', size: '413×579PX', hot: false }
      ],
      10: [ // 驾驶证照
        { id: 29, name: '驾驶证', size: '260×378PX', hot: false },
        { id: 30, name: '行驶证', size: '260×378PX', hot: false }
      ],
      11: [ // 护照签证
        { id: 31, name: '护照照片', size: '390×567PX', hot: false },
        { id: 32, name: '签证照片', size: '390×567PX', hot: false }
      ],
      12: [ // 学生证件
        { id: 33, name: '学生证', size: '260×378PX', hot: false },
        { id: 34, name: '校园卡', size: '260×378PX', hot: false }
      ],
      13: [ // 工作证件
        { id: 35, name: '工作证', size: '260×378PX', hot: false },
        { id: 36, name: '员工卡', size: '260×378PX', hot: false }
      ],
      14: [ // 社保医保
        { id: 37, name: '社保卡', size: '358×441PX', hot: false },
        { id: 38, name: '医保卡', size: '358×441PX', hot: false }
      ],
      15: [ // 银行金融
        { id: 39, name: '银行开户', size: '413×579PX', hot: false },
        { id: 40, name: '信用卡申请', size: '413×579PX', hot: false }
      ]
    }
  },
  // 轮播图点击事件
  onSwiperTap(e) {
    const { index, item } = e.currentTarget.dataset
    console.log('点击了轮播图', index, item)

    // 如果有跳转链接，则进行页面跳转
    if (item.url) {
      wx.navigateTo({
        url: item.url,
        fail: () => {
          wx.showToast({
            title: '页面不存在',
            icon: 'none'
          })
        }
      })
    } else {
      wx.showToast({
        title: `点击了第${index + 1}张图片`,
        icon: 'none'
      })
    }
  },

  // 主要功能按钮点击事件
  onMainActionTap() {
    console.log('点击了主要功能')
    wx.showToast({
      title: '主要功能',
      icon: 'none'
    })
    // 这里可以跳转到主要功能页面
    // wx.navigateTo({
    //   url: '/pages/main-service/main-service'
    // })
  },

  // 快捷功能点击事件
  onQuickActionTap(e) {
    const { type } = e.currentTarget.dataset
    console.log('点击了快捷功能:', type)

    const actionMap = {
      service1: {
        title: '数据统计',
        url: '/pages/statistics/statistics'
      },
      service2: {
        title: '系统设置',
        url: '/pages/settings/settings'
      }
    }

    const action = actionMap[type]
    if (action) {
      wx.showToast({
        title: action.title,
        icon: 'none'
      })
      // 这里可以跳转到对应页面
      // wx.navigateTo({
      //   url: action.url
      // })
    }
  },

  // 标签点击事件
  onTabTap(e) {
    const { index, id } = e.currentTarget.dataset
    console.log('点击了标签:', index, id)

    const allFilterTabs = this.data.allFilterTabs

    // 更新标签状态
    allFilterTabs.forEach((item) => {
      item.active = item.id === id
    })

    // 更新当前显示的尺寸列表
    const currentSizeList = this.data.allSizeData[id] || []

    // 更新可见标签
    this.updateVisibleTabs()

    this.setData({
      allFilterTabs: allFilterTabs,
      currentSizeList: currentSizeList
    })
  },

  // 切换分类显示状态
  onToggleCategories() {
    const showAllCategories = !this.data.showAllCategories
    this.setData({
      showAllCategories: showAllCategories
    })
    this.updateVisibleTabs()
  },

  // 更新可见标签
  updateVisibleTabs() {
    const { allFilterTabs, showAllCategories } = this.data
    let visibleTabs = []

    if (showAllCategories) {
      // 显示所有标签
      visibleTabs = [...allFilterTabs]
    } else {
      // 只显示前6个标签
      visibleTabs = allFilterTabs.slice(0, 6)
    }

    this.setData({
      visibleTabs: visibleTabs
    })
  },

  // 尺寸项点击事件
  onSizeItemTap(e) {
    const { item } = e.currentTarget.dataset
    console.log('点击了尺寸:', item)

    wx.showToast({
      title: `选择了${item.name}`,
      icon: 'none'
    })

    // 这里可以跳转到制作页面
    // wx.navigateTo({
    //   url: `/pages/photo-make/photo-make?size=${item.size}&name=${item.name}`
    // })
  },

  onLoad() {
    // 初始化显示热门尺寸和可见标签
    this.updateVisibleTabs()
    this.setData({
      currentSizeList: this.data.allSizeData[1] || []
    })
  }
})
