// index.js
// 获取应用实例
const app = getApp()

Page({
  data: {
    motto: 'Hello World',
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    canIUseGetUserProfile: false,
    canIUseOpenData: wx.canIUse('open-data.type.userAvatarUrl') && wx.canIUse('open-data.type.userNickName'), // 如需尝试获取用户信息可改为false

    // 轮播图配置
    indicatorDots: true,        // 是否显示面板指示点
    autoplay: true,             // 是否自动切换
    interval: 3000,             // 自动切换时间间隔
    duration: 500,              // 滑动动画时长
    circular: true,             // 是否采用衔接滑动
    indicatorColor: 'rgba(255, 255, 255, 0.5)',      // 指示点颜色
    indicatorActiveColor: '#ffffff',                   // 当前选中的指示点颜色

    // 轮播图数据
    swiperList: [
      {
        id: 1,
        image: 'https://img1.baidu.com/it/u=1966616150,2146512490&fm=26&fmt=auto&gp=0.jpg',
        title: '轮播图1',
        desc: '这是第一张轮播图的描述',
        url: '/pages/detail/detail?id=1'
      },
      {
        id: 2,
        image: 'https://img2.baidu.com/it/u=2967689259,2171491969&fm=26&fmt=auto&gp=0.jpg',
        title: '轮播图2',
        desc: '这是第二张轮播图的描述',
        url: '/pages/detail/detail?id=2'
      },
      {
        id: 3,
        image: 'https://img0.baidu.com/it/u=1956849617,4054837746&fm=26&fmt=auto&gp=0.jpg',
        title: '轮播图3',
        desc: '这是第三张轮播图的描述',
        url: '/pages/detail/detail?id=3'
      }
    ],

    // 证件照分类数据
    categoryList: [
      {
        id: 1,
        name: '身份证照',
        icon: '🆔',
        count: 12,
        active: false,
        photos: [
          { id: 1, name: '标准版', thumbnail: 'https://img1.baidu.com/it/u=1966616150,2146512490&fm=26&fmt=auto&gp=0.jpg' },
          { id: 2, name: '高清版', thumbnail: 'https://img2.baidu.com/it/u=2967689259,2171491969&fm=26&fmt=auto&gp=0.jpg' },
          { id: 3, name: '白底版', thumbnail: 'https://img0.baidu.com/it/u=1956849617,4054837746&fm=26&fmt=auto&gp=0.jpg' }
        ]
      },
      {
        id: 2,
        name: '护照照片',
        icon: '📘',
        count: 8,
        active: false,
        photos: [
          { id: 4, name: '标准护照', thumbnail: 'https://img1.baidu.com/it/u=1966616150,2146512490&fm=26&fmt=auto&gp=0.jpg' },
          { id: 5, name: '儿童护照', thumbnail: 'https://img2.baidu.com/it/u=2967689259,2171491969&fm=26&fmt=auto&gp=0.jpg' }
        ]
      },
      {
        id: 3,
        name: '驾驶证照',
        icon: '🚗',
        count: 6,
        active: false,
        photos: [
          { id: 6, name: '标准版', thumbnail: 'https://img0.baidu.com/it/u=1956849617,4054837746&fm=26&fmt=auto&gp=0.jpg' },
          { id: 7, name: '更新版', thumbnail: 'https://img1.baidu.com/it/u=1966616150,2146512490&fm=26&fmt=auto&gp=0.jpg' }
        ]
      },
      {
        id: 4,
        name: '学生证照',
        icon: '🎓',
        count: 15,
        active: false,
        photos: [
          { id: 8, name: '大学版', thumbnail: 'https://img2.baidu.com/it/u=2967689259,2171491969&fm=26&fmt=auto&gp=0.jpg' },
          { id: 9, name: '中学版', thumbnail: 'https://img0.baidu.com/it/u=1956849617,4054837746&fm=26&fmt=auto&gp=0.jpg' },
          { id: 10, name: '小学版', thumbnail: 'https://img1.baidu.com/it/u=1966616150,2146512490&fm=26&fmt=auto&gp=0.jpg' }
        ]
      },
      {
        id: 5,
        name: '工作证照',
        icon: '💼',
        count: 9,
        active: false,
        photos: [
          { id: 11, name: '正装版', thumbnail: 'https://img2.baidu.com/it/u=2967689259,2171491969&fm=26&fmt=auto&gp=0.jpg' },
          { id: 12, name: '休闲版', thumbnail: 'https://img0.baidu.com/it/u=1956849617,4054837746&fm=26&fmt=auto&gp=0.jpg' }
        ]
      }
    ]
  },
  // 轮播图点击事件
  onSwiperTap(e) {
    const { index, item } = e.currentTarget.dataset
    console.log('点击了轮播图', index, item)

    // 如果有跳转链接，则进行页面跳转
    if (item.url) {
      wx.navigateTo({
        url: item.url,
        fail: () => {
          wx.showToast({
            title: '页面不存在',
            icon: 'none'
          })
        }
      })
    } else {
      wx.showToast({
        title: `点击了第${index + 1}张图片`,
        icon: 'none'
      })
    }
  },

  // 主要功能按钮点击事件
  onMainActionTap() {
    console.log('点击了主要功能')
    wx.showToast({
      title: '主要功能',
      icon: 'none'
    })
    // 这里可以跳转到主要功能页面
    // wx.navigateTo({
    //   url: '/pages/main-service/main-service'
    // })
  },

  // 快捷功能点击事件
  onQuickActionTap(e) {
    const { type } = e.currentTarget.dataset
    console.log('点击了快捷功能:', type)

    const actionMap = {
      service1: {
        title: '数据统计',
        url: '/pages/statistics/statistics'
      },
      service2: {
        title: '系统设置',
        url: '/pages/settings/settings'
      }
    }

    const action = actionMap[type]
    if (action) {
      wx.showToast({
        title: action.title,
        icon: 'none'
      })
      // 这里可以跳转到对应页面
      // wx.navigateTo({
      //   url: action.url
      // })
    }
  },

  // 分类点击事件
  onCategoryTap(e) {
    const { index, id } = e.currentTarget.dataset
    console.log('点击了分类:', index, id)

    const categoryList = this.data.categoryList

    // 切换当前分类的展开状态
    categoryList[index].active = !categoryList[index].active

    // 关闭其他分类
    categoryList.forEach((item, i) => {
      if (i !== index) {
        item.active = false
      }
    })

    this.setData({
      categoryList: categoryList
    })
  },

  // 照片点击事件
  onPhotoTap(e) {
    const { photo } = e.currentTarget.dataset
    console.log('点击了照片:', photo)

    wx.showToast({
      title: `选择了${photo.name}`,
      icon: 'none'
    })

    // 这里可以跳转到照片详情页面或进行其他操作
    // wx.navigateTo({
    //   url: `/pages/photo-detail/photo-detail?id=${photo.id}`
    // })
  },

  // 事件处理函数
  bindViewTap() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },
  onLoad() {
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
  },
  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        console.log(res)
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },
  getUserInfo(e) {
    // 不推荐使用getUserInfo获取用户信息，预计自2021年4月13日起，getUserInfo将不再弹出弹窗，并直接返回匿名的用户个人信息
    console.log(e)
    this.setData({
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  }
})
