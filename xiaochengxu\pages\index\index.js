// index.js
// 获取应用实例
const app = getApp()

Page({
  data: {
    motto: 'Hello World',
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    canIUseGetUserProfile: false,
    canIUseOpenData: wx.canIUse('open-data.type.userAvatarUrl') && wx.canIUse('open-data.type.userNickName'), // 如需尝试获取用户信息可改为false

    // 轮播图配置
    indicatorDots: true,        // 是否显示面板指示点
    autoplay: true,             // 是否自动切换
    interval: 3000,             // 自动切换时间间隔
    duration: 500,              // 滑动动画时长
    circular: true,             // 是否采用衔接滑动
    indicatorColor: 'rgba(255, 255, 255, 0.5)',      // 指示点颜色
    indicatorActiveColor: '#ffffff',                   // 当前选中的指示点颜色

    // 轮播图数据
    swiperList: [
      {
        id: 1,
        image: 'https://img1.baidu.com/it/u=1966616150,2146512490&fm=26&fmt=auto&gp=0.jpg',
        title: '轮播图1',
        desc: '这是第一张轮播图的描述',
        url: '/pages/detail/detail?id=1'
      },
      {
        id: 2,
        image: 'https://img2.baidu.com/it/u=2967689259,2171491969&fm=26&fmt=auto&gp=0.jpg',
        title: '轮播图2',
        desc: '这是第二张轮播图的描述',
        url: '/pages/detail/detail?id=2'
      },
      {
        id: 3,
        image: 'https://img0.baidu.com/it/u=1956849617,4054837746&fm=26&fmt=auto&gp=0.jpg',
        title: '轮播图3',
        desc: '这是第三张轮播图的描述',
        url: '/pages/detail/detail?id=3'
      }
    ]
  },
  // 轮播图点击事件
  onSwiperTap(e) {
    const { index, item } = e.currentTarget.dataset
    console.log('点击了轮播图', index, item)

    // 如果有跳转链接，则进行页面跳转
    if (item.url) {
      wx.navigateTo({
        url: item.url,
        fail: () => {
          wx.showToast({
            title: '页面不存在',
            icon: 'none'
          })
        }
      })
    } else {
      wx.showToast({
        title: `点击了第${index + 1}张图片`,
        icon: 'none'
      })
    }
  },

  // 主要功能按钮点击事件
  onMainActionTap() {
    console.log('点击了主要功能')
    wx.showToast({
      title: '主要功能',
      icon: 'none'
    })
    // 这里可以跳转到主要功能页面
    // wx.navigateTo({
    //   url: '/pages/main-service/main-service'
    // })
  },

  // 快捷功能点击事件
  onQuickActionTap(e) {
    const { type } = e.currentTarget.dataset
    console.log('点击了快捷功能:', type)

    const actionMap = {
      service1: {
        title: '数据统计',
        url: '/pages/statistics/statistics'
      },
      service2: {
        title: '系统设置',
        url: '/pages/settings/settings'
      }
    }

    const action = actionMap[type]
    if (action) {
      wx.showToast({
        title: action.title,
        icon: 'none'
      })
      // 这里可以跳转到对应页面
      // wx.navigateTo({
      //   url: action.url
      // })
    }
  },

  // 事件处理函数
  bindViewTap() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },
  onLoad() {
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
  },
  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        console.log(res)
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },
  getUserInfo(e) {
    // 不推荐使用getUserInfo获取用户信息，预计自2021年4月13日起，getUserInfo将不再弹出弹窗，并直接返回匿名的用户个人信息
    console.log(e)
    this.setData({
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  }
})
